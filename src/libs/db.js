import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://hjcwqovjfemxqadmucxq.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhqY3dxb3ZqZmVteHFhZG11Y3hxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgzMDk0OTIsImV4cCI6MjA2Mzg4NTQ5Mn0.f9zpZA0YcXEkNCaoA3Jn3v8vpiAQI7ODj1ic_-dB0tY'
const supabase = createClient(supabaseUrl, supabaseKey)

// Fetch all pages
export async function fetchAllPages() {
  const { data: Pages, error } = await supabase
    .from('Pages')
    .select('*')
  return { Pages, error }
}

// Fetch pages by site_id
export async function fetchPagesBySiteId(id) {
  const { data: Pages, error } = await supabase
    .from('Pages')
    .select('*')
    .eq('site_id', id)

  return { Pages, error }
}

export async function fetchPageById(id) {
  const { data: Pages, error } = await supabase
    .from('Pages')
    .select('*')
    .eq('id', id)

  return { Pages, error }
}

export async function insertPage(pageData) {
  const { data, error } = await supabase
    .from('Pages')
    .insert([pageData])  // Wrap in an array for bulk compatibility
    .select()            // Returns the inserted row(s)

  return { data, error }
}

export async function updatePage(pageData) {
  const { data, error } = await supabase
    .from('Pages')
    .update(pageData)
    .eq('id', pageData.id)
    .select()  // Returns the updated row(s)

  return { data, error }
}