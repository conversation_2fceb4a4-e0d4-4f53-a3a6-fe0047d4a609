import React from 'react';

// Pre-load all Widgets modules
const modules = import.meta.glob('/src/components/Widgets/*/*.jsx');

const loadComponent = (name) => {
    const path = `/src/components/Widgets/${name}/${name}.jsx`;
    
    if (!modules[path]) {
        console.error(`Component not found: ${path}`);
        // Create a fallback component without JSX syntax
        return React.lazy(() => Promise.resolve({
            default: () => React.createElement('div', null, `Component not found: ${name}`)
        }));
    }
    
    return React.lazy(modules[path]);
}

export default loadComponent;
