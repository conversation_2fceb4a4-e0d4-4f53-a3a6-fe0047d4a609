import { useState, Suspense } from 'react';

import './App.css';
import WidgetList from '~/components/WidgetList/WidgetList';
import ContainerWidget from '~/components/Widgets/ContainerWidget/ContainerWidget.jsx';
import { DndContext } from '@dnd-kit/core';
import components from '~/libs/components.js';
import loadComponent from '~/libs/loadComponent.js';

const App = () => {
  const [widgets, setWidgets] = useState([]);

  const handleDragEnd = (event) => {
    if (event.canceled) return;

    if (event.over && event.over.id === 'droppable') {
      const [group, index] = event.active.id.split('-');
      const widgetName = components[group][index].widget;

      setWidgets((prevWidgets) => [...prevWidgets, widgetName]);
    }
  }

  return (
    <>
      <DndContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-5 h-screen">
          <div className="col-span-1 border-r h-full">
            <WidgetList />
          </div>
          <div className="col-span-4">
            <ContainerWidget>
              {
                widgets.map((widget, index) => {
                  const Widget = loadComponent(widget);
                  return (
                    <Suspense fallback={<div>Loading widget...</div>}>
                      <div className="p-1 border">
                        <Widget key={index} />
                      </div>
                    </Suspense>
                  )
                })
              }
            </ContainerWidget>
          </div>
        </div>
      </DndContext>
    </>
  );
}

export default App
