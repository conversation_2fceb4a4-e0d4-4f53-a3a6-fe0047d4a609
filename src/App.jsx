import { Routes, Route } from 'react-router-dom';
import Home from '~/pages/Home.jsx';
import Pages from '~/pages/Pages.jsx';
import AddPage from '~/pages/AddPage.jsx';
import EditPage from '~/pages/EditPage.jsx';

function App() {
  return (
    <>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/pages" element={<Pages />} />
        <Route path="/pages/add" element={<AddPage />} />
        <Route path="/pages/:id/edit" element={<EditPage />} />
      </Routes>
    </>
  );
}

export default App;