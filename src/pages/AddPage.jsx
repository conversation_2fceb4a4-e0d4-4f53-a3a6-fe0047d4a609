
import Sidebar from "~/components/Sidebar/Sidebar.jsx";
import PageForm from "~/components/Pages/PagesForm.jsx";
import { insertPage } from '~/libs/db.js';

const initalState = {
    title: "",
    slug: "",
    meta_title: "",
    meta_description: "",
    meta_keywords: "",
    site_id: 1,
};

const AddPage = () => {
    const handleSave = async (formData) => {
        const { data, error } = await insertPage(formData);

        if (error) {
            console.error('Insert failed:', error);
        } else {
            console.log('Inserted page:', data);
        }
    };

    return (
        <div className="flex">
            <Sidebar />
            <main className="flex-1 p-6 bg-gray-100">
                <h1 className="text-2xl font-semibold">Create New Page</h1>
                <PageForm mode="add" onSave={handleSave}  data={initalState} />
            </main>
        </div>
    );
};

export default AddPage;
