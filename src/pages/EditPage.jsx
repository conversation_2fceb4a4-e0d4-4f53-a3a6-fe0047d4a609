
import Sidebar from "~/components/Sidebar/Sidebar.jsx";
import PageForm from "~/components/Pages/PagesForm.jsx";
import { updatePage, fetchPageById } from "~/libs/db.js";
import { useState } from "react";
import { useParams } from "react-router-dom";

const initalState = {
    title: "",
    slug: "",
    meta_title: "",
    meta_description: "",
    meta_keywords: "",
    site_id: 1,
};

const AddPage = () => {
    const [pageData, setPageData] = useState(initalState);
    const [loading, setLoading] = useState(true)
    const { id } = useParams();

    const fetchPage = async () => {
        const { Pages, error } = await fetchPageById(id);
        if (error) {
            console.error("Fetch failed:", error);
        } else {
            setPageData(Pages[0]);
        }
        setLoading(false);
    }

    const handleSave = async (formData) => {
        const { data, error } = await updatePage(formData);

        if (error) {
            console.error("Insert failed:", error);
        } else {
            console.log("Inserted page:", data);
        }
    };

    fetchPage();

    return (
        <div className="flex">
            <Sidebar />
            <main className="flex-1 p-6 bg-gray-100">
                <h1 className="text-2xl font-semibold">Edit Page</h1>
                {loading ? (
                    <div>Loading...</div>
                ) : (
                    <PageForm mode="edit" onSave={handleSave} data={pageData} />
                )}
            </main>
        </div>
    );
};

export default AddPage;
