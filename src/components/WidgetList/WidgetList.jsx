import { Suspense } from 'react';

import components from '~/libs/components.js';
import Accordian from "~/components/Accordian/Accordian.jsx";
import DynamicIcon from '~/components/Icons/DynamicIcon';
import { Draggable } from '~/components/DragDrop/Draggable';

const WidgetList = () => {
    const componentGroup = Object.entries(components);
    return (
        <>
            {
                componentGroup.map(([group, items], index) => {
                    return (
                        <div key={group}>
                            <Accordian initialOpen={index == 0} title={group} children={widgetListItem(items, group)} />
                        </div>
                    )
                })
            }
        </>
    );
};

const widgetListItem = (items, group) => {
    return (
        <div className="grid grid-cols-2 gap-2">
            {
                items.map((item, index) => {
                    // const Widget = loadComponent(item.widget)
                    return (
                        <Suspense key={index} fallback={<div>Loading...</div>}>
                            <Draggable id={`${group}-${index}`}>
                                <div className="border border-black/40 rounded text-center flex flex-col gap-2 py-4 px-1">
                                    <DynamicIcon name={item.icon} className="text-black mx-auto" />
                                    <div>{item.title}</div>
                                    {/* <Widget /> */}
                                </div>
                            </Draggable>
                        </Suspense>
                    );
                })
            }
        </div>
    )
}

export default WidgetList;
