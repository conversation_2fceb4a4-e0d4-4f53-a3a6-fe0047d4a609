import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import * as Icons from "lucide-react";
import {
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  ChevronUp,
} from "lucide-react";

const menuItems = [
  {
    label: "Dashboard",
    icon: "Home",
    path: "/",
  },
  {
    label: "Pages",
    icon: "Layout",
    path: "/pages",
  },
//   {
//     label: "Pages",
//     icon: "Layout",
//     children: [
//       { label: "Page 1", path: "/pages/page1" },
//       { label: "Page 2", path: "/pages/page2" },
//     ],
//   },
];

const Sidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [openMenus, setOpenMenus] = useState({});
  const location = useLocation();

  const toggleSidebar = () => setIsCollapsed(!isCollapsed);

  const toggleMenu = (label) => {
    setOpenMenus((prev) => ({
      ...prev,
      [label]: !prev[label],
    }));
  };

  return (
    <aside
      className={`${
        isCollapsed ? "w-16" : "w-64"
      } h-screen bg-zinc-900 text-white flex flex-col shadow-lg transition-all duration-300`}
    >
      <div className="p-6 flex items-center justify-between border-b border-gray-800">
        <button
          onClick={toggleSidebar}
          className="p-2 text-gray-300 hover:text-white focus:outline-none"
        >
          {isCollapsed ? <ChevronRight /> : <ChevronLeft />}
        </button>
      </div>

      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = Icons[item.icon] || Icons.Circle;
            const isOpen = openMenus[item.label];

            return (
              <li key={item.label}>
                {item.children ? (
                  <>
                    <button
                      onClick={() => toggleMenu(item.label)}
                      className="w-full flex items-center p-2 text-gray-300 hover:bg-gray-800 hover:text-white rounded-md transition-colors"
                    >
                      <Icon className="w-5 h-5" />
                      {!isCollapsed && (
                        <>
                          <span className="flex-1 text-left ml-2">{item.label}</span>
                          {isOpen ? <ChevronUp /> : <ChevronDown />}
                        </>
                      )}
                    </button>
                    {!isCollapsed && isOpen && (
                      <ul className="ml-6 mt-2 space-y-2">
                        {item.children.map((child) => (
                          <li key={child.label}>
                            <Link
                              to={child.path}
                              className={`flex items-center p-2 text-gray-400 hover:bg-gray-700 hover:text-white rounded-md transition-colors ${
                                location.pathname === child.path ? "bg-gray-800 text-white" : ""
                              }`}
                            >
                              {child.label}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    )}
                  </>
                ) : (
                  <Link
                    to={item.path}
                    className={`flex items-center p-2 text-gray-300 hover:bg-gray-800 hover:text-white rounded-md transition-colors ${
                      location.pathname === item.path ? "bg-gray-800 text-white" : ""
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {!isCollapsed && <span className="ml-2">{item.label}</span>}
                  </Link>
                )}
              </li>
            );
          })}
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;
