const TextWidget = (props) => {
    return (
        <div className="p-4 bg-gray-50 rounded border">
            <div className="text-lg font-medium">Text Widget</div>
            <div className="text-xs text-gray-500 mt-1">
                This is a sample text widget that can contain any text content.
            </div>
            {props.id && (
                <div className="text-xs text-gray-400 mt-2">ID: {props.id}</div>
            )}
        </div>
    )
}

export default TextWidget