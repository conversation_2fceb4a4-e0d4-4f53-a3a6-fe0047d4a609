// import DynamicIcon from '~/components/Icons/DynamicIcon';
import { Suspense } from 'react';
import { Droppable } from '~/components/DragDrop/Droppable.jsx';

const ContainerWidget = (props) => {
    return (
        <Suspense fallback={<div>Loading widget...</div>}>
            <Droppable id={props.id}>
                <div className="min-h-[200px]">
                    {props.children}
                </div>
            </Droppable >
        </Suspense>
    )
}

export default ContainerWidget;