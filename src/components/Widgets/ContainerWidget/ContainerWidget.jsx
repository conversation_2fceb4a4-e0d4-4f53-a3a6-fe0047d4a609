// import DynamicIcon from '~/components/Icons/DynamicIcon';
import { Suspense } from 'react';
import { Droppable } from '~/components/DragDrop/Droppable.jsx';

const ContainerWidget = (props) => {
    // const isRootContainer = props.id === 'root-container';
    const isContainerWidget = props.type === 'ContainerWidget';

    return (
        <Suspense fallback={<div>Loading widget...</div>}>
            <Droppable id={props.id}>
                <div className={`
                    ${isContainerWidget ? 'border border-gray-300 border-dashed p-2 min-h-[200px]' : ''}
                    ${isContainerWidget && !props.children?.length ? 'flex items-center justify-center' : ''}
                `}>
                    {!props.children?.length && (
                        <div className="text-center h-">
                            <div className="text-sm">Drop widgets here</div>
                            <div className="text-xs text-gray-400">Container ID: {props.type}</div>
                        </div>
                    )}
                    {props.children}
                </div>
            </Droppable >
        </Suspense>
    )
}

export default ContainerWidget;