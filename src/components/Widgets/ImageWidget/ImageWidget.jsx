const ImageWidget = (props) => {
    return (
        <div className="p-4 bg-gray-50 rounded border">
            <div className="text-lg font-medium">Image Widget</div>
            <div className="w-full h-32 bg-gray-200 rounded mt-2 flex items-center justify-center">
                <div className="text-gray-500 text-sm">Image Placeholder</div>
            </div>
            <div className="text-xs text-gray-500 mt-2">
                This widget would display an image in a real application.
            </div>
            {props.id && (
                <div className="text-xs text-gray-400 mt-2">ID: {props.id}</div>
            )}
        </div>
    )
}

export default ImageWidget;