import { Suspense } from 'react';
import loadComponent from '~/libs/loadComponent.js';
import { Droppable } from '~/components/DragDrop/Droppable.jsx';

const WidgetCanvas = (props) => {
    // This could be dynamic based on user selection
    const widgetName = "ContainerWidget";
    const Widget = loadComponent(widgetName);

    return (
        <div className="p-4">
            <div className="border border-gray-300 p-4 min-h-[300px]">
                <Suspense fallback={<div>Loading widget...</div>}>
                    <Droppable >
                        <Widget>
                            {props.children}
                        </Widget>
                    </Droppable >
                </Suspense>
            </div>
        </div>
    );
}

export default WidgetCanvas;
