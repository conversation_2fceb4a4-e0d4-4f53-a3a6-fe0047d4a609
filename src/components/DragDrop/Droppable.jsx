import React from 'react';
import { useDroppable } from '@dnd-kit/core';

export function Droppable(props) {
  const { isOver, setNodeRef } = useDroppable({
    id: props.id || 'droppable',
  });

  return (
    <div
      ref={setNodeRef}
      className={`rounded transition-colors duration-200 ${
        isOver ? 'bg-blue-100 ring-2 ring-blue-400 ring-opacity-50' : ''
      }`}
    >
      {props.children}
    </div>
  );
}