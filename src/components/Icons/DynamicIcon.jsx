import React, { Suspense } from 'react';

const loadIcon = (iconName) => {
  return React.lazy(() =>
    import('lucide-react').then(module => ({
      default: module[iconName],
    }))
  );
};

const DynamicIcon = ({ name, className }) => {
  const Icon = loadIcon(name);

  return (
    <Suspense fallback={<div className="inline-block w-6 h-6 animate-pulse mx-auto">⏳</div>}>
      <Icon className={className ?? 'w-6 h-6 text-blue-500'} />
    </Suspense>
  );
};

export default DynamicIcon;