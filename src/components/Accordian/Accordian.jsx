import React, { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

const Accordion = ({ title, children, initialOpen }) => {
  const [isOpen, setIsOpen] = useState(initialOpen);

  return (
    <div className="rounded mb-2">
      <button
        className="w-full flex justify-between items-center p-4 font-medium text-left bg-gray-100 hover:bg-gray-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="uppercase">{title}</span>
        {isOpen ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
      </button>
      {isOpen && (
        <div className="p-4 bg-white text-sm">
          {children}
        </div>
      )}
    </div>
  );
};

export default Accordion;
