import { useState, useEffect } from "react";

const PageForm = ({ mode, data, onSave }) => {

    const [formData, setFormData] = useState(data);
    const [errors, setErrors] = useState({});
    const [slugEdited, setSlugEdited] = useState(false);

    if (mode === 'edit') {
        setSlugEdited(true);
    }

    // Auto-generate slug from title
    useEffect(() => {
        if (slugEdited) return;
        const slug = formData.title
            .toLowerCase()
            .trim()
            .replace(/[^\w\s-]/g, "")
            .replace(/\s+/g, "-");

        setFormData((prev) => ({ ...prev, slug }));
    }, [formData.title, slugEdited]);

    const handleChange = (e) => {
        const { name, value } = e.target;

        // If user edits slug manually, stop auto-generation
        if (name === "slug") setSlugEdited(true);

        setFormData((prev) => ({ ...prev, [name]: value }));

        if (name === "title") {
            // Validate this single field immediately
            validateField("slug", formData.slug);
        }
        validateField(name, value);
    };

    const validateField = (name, value) => {
        let message = "";

        if (name === "title" && !value.trim()) {
            message = "Title is required";
        }

        if (name === "slug" && !value.trim()) {
            message = "Slug is required";
        }

        // Update only the error for the current field
        setErrors((prev) => ({ ...prev, [name]: message }));
    };

    const validate = () => {
        const newErrors = {};
        if (!formData.title.trim()) newErrors.title = "Title is required";
        if (!formData.slug.trim()) newErrors.slug = "Slug is required";
        return newErrors;
    };

    const handleSave = async () => {
        const validationErrors = validate();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        onSave(formData);
    };

    return (
        <div className="w-full mt-2 bg-white p-6 rounded-md shadow space-y-4">

            {/* Title */}
            <div>
                <label className="block text-sm font-medium mb-1">Title</label>
                <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="Page title"
                />
                {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
            </div>

            {/* Slug */}
            <div>
                <label className="block text-sm font-medium mb-1">URL Slug</label>
                <input
                    type="text"
                    name="slug"
                    value={formData.slug}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                    placeholder="auto-generated slug"
                />
                {errors.slug && <p className="text-red-500 text-sm mt-1">{errors.slug}</p>}
            </div>

            {/* Meta Title */}
            <div>
                <label className="block text-sm font-medium mb-1">Meta Title</label>
                <input
                    type="text"
                    name="meta_title"
                    value={formData.meta_title}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                />
            </div>

            {/* Meta Description */}
            <div>
                <label className="block text-sm font-medium mb-1">Meta Description</label>
                <textarea
                    name="meta_description"
                    value={formData.meta_description}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                ></textarea>
            </div>

            {/* Meta Keywords */}
            <div>
                <label className="block text-sm font-medium mb-1">Meta Keywords</label>
                <input
                    type="text"
                    name="meta_keywords"
                    value={formData.meta_keywords}
                    onChange={handleChange}
                    className="w-full border border-gray-300 rounded px-3 py-2"
                />
            </div>

            <div className="pt-4">
                <button
                    onClick={handleSave}
                    className="bg-zinc-900 hover:bg-zinc-950 hover:cursor-pointer text-white font-medium px-4 py-2 rounded"
                >
                    Save
                </button>
            </div>
        </div>
    );
};

export default PageForm;
