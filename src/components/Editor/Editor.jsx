import { useState, Suspense } from 'react';
import WidgetList from '~/components/WidgetList/WidgetList';
import { DndContext } from '@dnd-kit/core';
import components from '~/libs/components.js';
import loadComponent from '~/libs/loadComponent.js';
import { generateId } from '~/libs/utils.js';

const Editor = () => {
  const [widgets, setWidgets] = useState([
    {
      id: "root-container",
      type: "ContainerWidget",
      children: []
    }
  ]);

  const handleDragEnd = (event) => {
    if (event.canceled) return;

    const targetId = event?.over?.id;
    if (!targetId) return;

    const [group, index] = event.active.id.split('-');
    const widgetName = components[group][index].widget;

    const newWidget = {
      id: generateId(),
      type: widgetName,
    };

    if (widgetName === 'ContainerWidget') {
      newWidget.children = [];
    }

    setWidgets(prev => addWidget(prev, targetId, newWidget));
  }

  const addWidget = (tree, targetId, newWidget) => {
    return tree.map((node) => {
      if (node.id === targetId) {
        // Insert into this node's children
        return {
          ...node,
          children: [...(node.children || []), newWidget]
        };
      }

      // Recurse into children if they exist
      if (node.children && node.children.length > 0) {
        return {
          ...node,
          children: addWidget(node.children, targetId, newWidget)
        };
      }

      return node; // unchanged
    });
  };

  // Recursive widget renderer
  const RenderWidgetTree = ({ widget }) => {
    const Widget = loadComponent(widget.type);

    return (
      <Suspense fallback={<div>Loading widget...</div>}>
        <div className="p-1" key={widget.id}>
          <Widget id={widget.id} type={widget.type}>
            {
              widget.children && widget.children.length > 0 &&
              widget.children.map((child) => (
                <RenderWidgetTree key={child.id} widget={child} />
              ))
            }
          </Widget>
        </div>
      </Suspense>
    );
  };

  return (
    <>
      <DndContext onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-5 h-screen">
          <div className="col-span-1 border-r h-full">
            <WidgetList />
          </div>

          <div className="col-span-4 p-4">
            {
              widgets.map((widget) => (
                <RenderWidgetTree key={widget.id} widget={widget} />
              ))
            }
          </div>
        </div>
      </DndContext>
    </>
  );
}

export default Editor
